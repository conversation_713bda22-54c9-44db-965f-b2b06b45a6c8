# 原型学习和静态任务图训练系统 - 技术文档

**版本**: 1.0
**更新时间**: 2024年12月19日
**适用环境**: conda环境

## 目录

1. [系统概述](#系统概述)
2. [任务图设计与训练机制](#任务图设计与训练机制)
3. [模型性能评估体系](#模型性能评估体系)
4. [代码示例和配置说明](#代码示例和配置说明)
5. [故障排除指南](#故障排除指南)
6. [详细技术报告模板](#详细技术报告模板)

---

## 系统概述

### 核心技术架构

本系统实现了一个基于原型学习和静态任务图的程序性视频理解框架，主要包含以下核心组件：

#### 1. 多视角特征融合模块

- **功能**: 处理来自不同视角的视频特征
- **方法**: 简化融合策略（concat/mean）
- **优势**: 避免复杂注意力机制，提高训练稳定性

#### 2. 原型学习模块

- **功能**: 学习每个动作类别的原型表示
- **方法**: 对比学习 + 动量更新
- **优势**: 提高特征区分度和泛化能力

#### 3. 静态任务图学习模块

- **功能**: 建模动作间的依赖关系和执行顺序
- **方法**: 简化图神经网络（不依赖torch_geometric）
- **优势**: 降低部署复杂度，便于理解和扩展

#### 4. 时序特征编码器

- **功能**: 提取视频序列的时序特征
- **方法**: 双向LSTM
- **优势**: 捕获长期时序依赖关系

### 系统特点

1. **简化架构设计**: 移除复杂注意力机制，专注核心功能
2. **模块化实现**: 各功能独立封装，易于扩展和维护
3. **实际数据适配**: 基于真实breakfast数据集验证
4. **全面评估体系**: 多维度指标和可视化分析

**注意**: 由于本系统使用LSTM进行时序建模，使用简化的多视角融合和GCN进行图学习，**不包含任何注意力机制**，因此不需要也不支持Flash Attention优化。

### 系统优化特性

#### 智能批次大小调整

- **数据集分析**: 自动分析训练集和测试集的序列长度分布
- **预防性调整**: 根据数据特征预先调整批次大小，避免内存溢出
- **双重验证**: 同时在训练集和测试集上验证批次大小的可行性
- **自动恢复**: CUDA内存溢出时自动减半批次大小直到成功

#### 文档一致性保证

- **统一输出结构**: 所有脚本和文档使用一致的输出目录结构
- **自动验证**: 部署脚本自动创建标准化的输出目录

---

## 任务图设计与训练机制

### 静态任务图构建原理

#### 1. 图结构定义

**节点定义**:

- 每个节点代表一个动作类别
- 节点特征由动作原型表示
- 支持动态节点嵌入更新

**边定义**:

- 有向边表示动作间的时序依赖关系
- 边权重表示依赖强度
- 支持稀疏图结构学习

#### 2. 图神经网络架构

```python
class TaskGraphEncoder(nn.Module):
    def __init__(self, num_actions, hidden_dim, num_gnn_layers):
        # 动作节点嵌入
        self.action_embedding = nn.Embedding(num_actions, hidden_dim)

        # 简化GCN层
        self.gnn_layers = nn.ModuleList([
            GCNConv(hidden_dim, hidden_dim)
            for _ in range(num_gnn_layers)
        ])

        # 边概率预测器
        self.edge_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
```

#### 3. 训练流程详解

##### 步骤1: 动作序列编码

```python
# 输入: 动作序列 [batch_size, seq_len]
action_embeddings = self.action_embedding(action_sequences)

# 时序聚合
sequence_features = self.temporal_aggregator(action_embeddings)
```

##### 步骤2: 图结构学习

```python
# 计算所有动作对的边概率
edge_probs = []
for i in range(num_actions):
    for j in range(num_actions):
        edge_feature = torch.cat([node_features[i], node_features[j]], dim=-1)
        prob = self.edge_predictor(edge_feature)
        edge_probs.append(prob)
```

##### 步骤3: 图卷积传播

```python
# 多层图卷积
node_features = action_embeddings
for gnn_layer in self.gnn_layers:
    node_features = gnn_layer(node_features, edge_index, edge_weights)
    node_features = F.relu(node_features)
```

### 原型学习算法

#### 1. 原型初始化

```python
# 为每个动作类别初始化多个原型
prototypes = nn.Parameter(
    torch.randn(num_actions, num_prototypes_per_action, prototype_dim)
)
```

#### 2. 原型更新机制

```python
def update_prototypes(self, features, labels):
    for action_id in range(self.num_actions):
        # 选择属于当前动作的特征
        action_mask = (labels == action_id)
        if action_mask.sum() > 0:
            action_features = features[action_mask]

            # 计算与原型的相似度
            similarities = torch.mm(action_features, self.prototypes[action_id].T)

            # 软分配到最近的原型
            assignments = F.softmax(similarities / self.temperature, dim=-1)

            # 动量更新原型
            for proto_idx in range(self.num_prototypes_per_action):
                weighted_features = (assignments[:, proto_idx:proto_idx+1] * action_features).sum(0)
                self.prototypes[action_id, proto_idx] = (
                    self.momentum * self.prototypes[action_id, proto_idx] +
                    (1 - self.momentum) * weighted_features
                )
```

#### 3. 对比学习损失

```python
def contrastive_loss(self, features, labels, prototypes):
    # 计算特征与所有原型的相似度
    similarities = torch.mm(features, prototypes.view(-1, self.prototype_dim).T)

    # 构建正负样本标签
    targets = self.create_prototype_targets(labels)

    # InfoNCE损失
    loss = F.cross_entropy(similarities / self.temperature, targets)
    return loss
```

### 简化融合方法

#### 1. 拼接融合 (Concat)

```python
def concat_fusion(self, view_features_list):
    # 直接拼接不同视角的特征
    fused_features = torch.cat(view_features_list, dim=-1)

    # 线性变换到目标维度
    fused_features = self.fusion_linear(fused_features)
    return fused_features
```

#### 2. 平均融合 (Mean)

```python
def mean_fusion(self, view_features_list):
    # 对不同视角特征求平均
    stacked_features = torch.stack(view_features_list, dim=0)
    fused_features = torch.mean(stacked_features, dim=0)
    return fused_features
```

### 训练流程步骤

#### 完整训练循环

```python
def training_step(self, batch):
    # 1. 多视角特征融合
    fused_features = self.multi_view_fusion(batch['features'])

    # 2. 时序特征编码
    temporal_features, _ = self.temporal_encoder(fused_features)

    # 3. 动作分类
    action_logits = self.action_classifier(temporal_features)

    # 4. 任务图学习
    task_graph_outputs = self.task_graph_encoder(
        batch['labels'], batch['sequence_length']
    )

    # 5. 原型学习
    prototype_outputs = self.prototype_learner(
        temporal_features, batch['labels'], update_prototypes=True
    )

    # 6. 损失计算
    losses = self.compute_total_loss(outputs, batch)

    return losses
```

### 配置参数说明

#### 任务图相关参数

```yaml
model:
  task_graph:
    hidden_dim: 512              # 隐藏层维度
    num_gnn_layers: 3           # GNN层数
    dropout: 0.1                # Dropout率
    graph_pooling: "mean"       # 图池化方法
    edge_threshold: 0.5         # 边概率阈值
```

#### 原型学习参数

```yaml
model:
  prototype:
    prototype_dim: 256          # 原型维度
    num_prototypes_per_action: 3 # 每个动作的原型数
    temperature: 0.1            # 温度参数
    momentum: 0.9               # 动量更新系数
```

#### 融合方法参数

```yaml
model:
  fusion:
    fusion_method: "concat"     # 融合方法: concat/mean
    view_encoder_dim: 256       # 视角编码器维度
    domain_adaptation: false    # 是否使用域适应
    uncertainty_weighting: false # 是否使用不确定性权重
```

---

## 模型性能评估体系

### 定量评估指标

#### 1. 准确率指标

##### 帧级别准确率 (Frame Accuracy)

```python
def compute_frame_accuracy(predictions, labels):
    # 忽略填充标签(-1)
    valid_mask = (labels >= 0)
    if valid_mask.sum() == 0:
        return 0.0

    valid_predictions = predictions[valid_mask]
    valid_labels = labels[valid_mask]

    accuracy = (valid_predictions == valid_labels).float().mean()
    return accuracy.item()
```

##### 段级别准确率 (Segment Accuracy)

段级别准确率使用标准IoU (Intersection over Union) 指标来判断段匹配，确保与学术界标准一致。

```python
def compute_segment_accuracy(predictions, labels):
    # 将帧级别预测转换为段级别
    pred_segments = self.frames_to_segments(predictions)
    true_segments = self.frames_to_segments(labels)

    # 计算段级别匹配度
    correct_segments = 0
    total_segments = len(true_segments)

    for true_seg in true_segments:
        for pred_seg in pred_segments:
            if self.segments_match(true_seg, pred_seg):
                correct_segments += 1
                break

    return correct_segments / total_segments if total_segments > 0 else 0.0

def segments_match(seg1, seg2, threshold=0.5):
    """
    使用标准IoU判断段匹配

    IoU = 交集长度 / 并集长度
    = overlap_length / (seg1_length + seg2_length - overlap_length)

    这与学术界标准一致，便于与其他研究成果进行公平比较
    """
    if seg1['action'] != seg2['action']:
        return False

    overlap_start = max(seg1['start'], seg2['start'])
    overlap_end = min(seg1['end'], seg2['end'])

    if overlap_start > overlap_end:
        return False

    overlap_length = overlap_end - overlap_start + 1
    seg1_length = seg1['end'] - seg1['start'] + 1
    seg2_length = seg2['end'] - seg2['start'] + 1

    # 标准IoU计算
    union_length = seg1_length + seg2_length - overlap_length
    iou = overlap_length / union_length if union_length > 0 else 0.0

    return iou >= threshold
```

#### 2. 精确率、召回率、F1分数

##### 多类别指标计算

```python
def compute_classification_metrics(predictions, labels, num_classes):
    from sklearn.metrics import precision_recall_fscore_support

    # 展平并过滤有效标签
    flat_predictions = predictions.flatten()
    flat_labels = labels.flatten()
    valid_mask = (flat_labels >= 0)

    valid_predictions = flat_predictions[valid_mask]
    valid_labels = flat_labels[valid_mask]

    # 计算各类别指标
    precision, recall, f1, support = precision_recall_fscore_support(
        valid_labels, valid_predictions,
        labels=list(range(num_classes)),
        average=None,
        zero_division=0
    )

    # 计算宏平均
    macro_precision = precision.mean()
    macro_recall = recall.mean()
    macro_f1 = f1.mean()

    return {
        'precision': macro_precision,
        'recall': macro_recall,
        'f1_score': macro_f1,
        'per_class_precision': precision,
        'per_class_recall': recall,
        'per_class_f1': f1
    }
```

#### 3. 编辑距离 (Edit Distance)

##### 序列相似度评估

```python
def compute_edit_distance(pred_sequence, true_sequence):
    """计算两个动作序列的编辑距离"""
    from Levenshtein import distance

    # 转换为字符串序列
    pred_str = ''.join([str(x) for x in pred_sequence if x >= 0])
    true_str = ''.join([str(x) for x in true_sequence if x >= 0])

    # 计算归一化编辑距离
    edit_dist = distance(pred_str, true_str)
    max_len = max(len(pred_str), len(true_str))

    normalized_distance = edit_dist / max_len if max_len > 0 else 0.0
    return normalized_distance
```

### 原型质量评估

#### 1. 原型内聚度 (Intra-class Cohesion)

```python
def evaluate_prototype_cohesion(self, action_id):
    """评估单个动作类别的原型内聚度"""
    prototypes = self.get_action_prototypes(action_id)

    # 计算原型间的平均距离
    distances = []
    for i in range(len(prototypes)):
        for j in range(i+1, len(prototypes)):
            dist = torch.norm(prototypes[i] - prototypes[j])
            distances.append(dist.item())

    # 内聚度 = 1 / (1 + 平均距离)
    avg_distance = np.mean(distances) if distances else 0
    cohesion = 1.0 / (1.0 + avg_distance)
    return cohesion
```

#### 2. 原型分离度 (Inter-class Separation)

```python
def evaluate_prototype_separation(self):
    """评估不同动作类别间的原型分离度"""
    all_prototypes = []
    action_labels = []

    for action_id in range(self.num_actions):
        prototypes = self.get_action_prototypes(action_id)
        all_prototypes.extend(prototypes)
        action_labels.extend([action_id] * len(prototypes))

    # 计算类间距离 vs 类内距离比值
    inter_distances = []
    intra_distances = []

    for i in range(len(all_prototypes)):
        for j in range(i+1, len(all_prototypes)):
            dist = torch.norm(all_prototypes[i] - all_prototypes[j]).item()

            if action_labels[i] == action_labels[j]:
                intra_distances.append(dist)
            else:
                inter_distances.append(dist)

    avg_inter = np.mean(inter_distances) if inter_distances else 0
    avg_intra = np.mean(intra_distances) if intra_distances else 1

    separation = avg_inter / avg_intra if avg_intra > 0 else 0
    return separation
```

### 可视化分析

#### 1. 训练曲线可视化

```python
def plot_training_curves(train_history):
    """绘制训练损失和准确率曲线"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    # 训练损失
    ax1.plot(train_history['epoch'], train_history['train_loss'], 'b-', linewidth=2)
    ax1.set_title('训练损失', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.grid(True, alpha=0.3)

    # 测试准确率
    ax2.plot(train_history['epoch'], train_history['test_accuracy'], 'r-', linewidth=2)
    ax2.set_title('测试准确率', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    return fig
```

#### 2. 混淆矩阵可视化

```python
def plot_confusion_matrix(predictions, labels, action_names):
    """绘制混淆矩阵"""
    from sklearn.metrics import confusion_matrix
    import seaborn as sns

    # 计算混淆矩阵
    cm = confusion_matrix(labels, predictions, labels=range(len(action_names)))

    # 绘制热力图
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=action_names, yticklabels=action_names)
    plt.title('混淆矩阵', fontsize=14, fontweight='bold')
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.tight_layout()
    return plt.gcf()
```

#### 3. 任务图结构可视化

```python
def visualize_task_graph(adjacency_matrix, action_names):
    """可视化学习到的任务图结构"""
    import networkx as nx

    # 创建有向图
    G = nx.DiGraph()

    # 添加节点
    for i, action in enumerate(action_names):
        G.add_node(i, label=action)

    # 添加边（基于邻接矩阵）
    for i in range(len(action_names)):
        for j in range(len(action_names)):
            if adjacency_matrix[i, j] > 0.5:  # 阈值过滤
                G.add_edge(i, j, weight=adjacency_matrix[i, j].item())

    # 绘制图
    plt.figure(figsize=(10, 8))
    pos = nx.spring_layout(G, k=2, iterations=50)

    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_color='lightblue',
                          node_size=1000, alpha=0.8)

    # 绘制边
    nx.draw_networkx_edges(G, pos, edge_color='gray',
                          arrows=True, arrowsize=20, alpha=0.6)

    # 绘制标签
    labels = {i: action_names[i] for i in range(len(action_names))}
    nx.draw_networkx_labels(G, pos, labels, font_size=10)

    plt.title('学习到的任务图结构', fontsize=14, fontweight='bold')
    plt.axis('off')
    plt.tight_layout()
    return plt.gcf()
```

### 评估流程实现

#### 完整评估函数

```python
def comprehensive_evaluation(model, test_loader, config):
    """执行全面的模型评估"""
    model.eval()

    all_predictions = []
    all_labels = []

    # 收集所有预测结果
    with torch.no_grad():
        for batch in test_loader:
            outputs = model(batch, mode='eval')
            predictions = outputs['action_predictions'].cpu().numpy()
            labels = batch['labels'].cpu().numpy()

            all_predictions.append(predictions)
            all_labels.append(labels)

    # 合并结果
    all_predictions = np.concatenate(all_predictions, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)

    # 计算各种指标
    metrics = {}

    # 1. 基础准确率
    metrics['frame_accuracy'] = compute_frame_accuracy(all_predictions, all_labels)
    metrics['segment_accuracy'] = compute_segment_accuracy(all_predictions, all_labels)

    # 2. 分类指标
    classification_metrics = compute_classification_metrics(
        all_predictions, all_labels, config['data']['num_actions']
    )
    metrics.update(classification_metrics)

    # 3. 编辑距离
    edit_distances = []
    for i in range(len(all_predictions)):
        edit_dist = compute_edit_distance(all_predictions[i], all_labels[i])
        edit_distances.append(edit_dist)
    metrics['edit_distance'] = np.mean(edit_distances)

    # 4. 原型质量评估
    prototype_metrics = evaluate_prototype_quality(model)
    metrics.update(prototype_metrics)

    return metrics

---

## 代码示例和配置说明

### 快速开始示例

#### 1. 环境准备
```bash
# 激活conda环境 (Linux服务器环境)
conda activate sci_1_server

# 验证环境
python --version  # 应显示Python 3.9+
pip list | grep torch  # 确认PyTorch安装
```

#### 2. 运行完整实验

```bash
# 运行完整训练实验 (A6000双卡)
python scripts/train_Static.py --profile=a6000 --amp --compile

# 使用服务器配置
python scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile

# 查看生成的结果
ls /data2/syd_data/Breakfast_Data/Outputs/checkpoints/  # 模型权重
ls /data2/syd_data/Breakfast_Data/Outputs/pictures/     # 图片和可视化
ls /data2/syd_data/Breakfast_Data/Outputs/reports/      # 技术报告
```

#### 3. 自定义配置运行

```python
#!/usr/bin/env python3
import yaml
import torch
from src.data_loader_fixed import create_data_loaders
from src.model import ProceduralVideoUnderstanding

# 加载并修改配置
with open('configs/default.yaml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)

# 自定义训练参数
config['training']['num_epochs'] = 20
config['training']['batch_size'] = 16
config['training']['learning_rate'] = 0.0005

# 创建数据加载器
train_loader, test_loader = create_data_loaders(config)

# 更新动作数量
config['data']['num_actions'] = len(train_loader.dataset.action_to_id)

# 创建模型
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = ProceduralVideoUnderstanding(config).to(device)

print(f"模型参数数: {sum(p.numel() for p in model.parameters()):,}")
```

### 配置文件详解

#### 完整配置结构

```yaml
# configs/default.yaml

# 数据配置
data:
  root_dir: "data/breakfast_data"           # 数据根目录
  feature_dim: 65                      # 特征维度
  max_sequence_length: 1000            # 最大序列长度
  num_actions: 5                       # 动作类别数（自动更新）
  train_views: ["s1", "s2", "s3"]     # 训练视角
  test_views: ["s4"]                   # 测试视角

# 模型配置
model:
  # 任务图学习
  task_graph:
    hidden_dim: 512                    # 隐藏层维度
    num_gnn_layers: 3                  # GNN层数
    dropout: 0.1                       # Dropout率
    graph_pooling: "mean"              # 图池化方法

  # 原型学习
  prototype:
    prototype_dim: 256                 # 原型维度
    num_prototypes_per_action: 3       # 每个动作的原型数
    temperature: 0.1                   # 温度参数
    momentum: 0.9                      # 动量更新系数

  # 多视角融合
  fusion:
    fusion_method: "concat"            # 融合方法: concat/mean
    view_encoder_dim: 256              # 视角编码器维度
    domain_adaptation: false           # 域适应
    uncertainty_weighting: false      # 不确定性权重

# 训练配置
training:
  num_epochs: 100                      # 训练轮数
  batch_size: 8                        # 批次大小
  learning_rate: 0.001                 # 学习率
  weight_decay: 0.0001                 # 权重衰减
  warmup_epochs: 5                     # 预热轮数
  scheduler: "cosine"                  # 学习率调度器

  # 损失权重
  loss_weights:
    action_classification: 1.0         # 动作分类损失权重
    prototype_contrastive: 0.5         # 原型对比损失权重
    task_graph_structure: 0.3          # 任务图结构损失权重
    temporal_consistency: 0.2          # 时序一致性损失权重
    view_alignment: 0.1                # 视角对齐损失权重

# 实验配置
experiment:
  name: "prototype_task_graph"         # 实验名称
  seed: 42                             # 随机种子
  save_dir: "experiments"              # 保存目录
  log_freq: 10                         # 日志频率
  checkpoint_freq: 10                  # 检查点保存频率
```

#### 关键参数调优指南

##### 学习率调优

```yaml
# 保守设置（推荐新手）
learning_rate: 0.0001
scheduler: "constant"

# 标准设置（推荐）
learning_rate: 0.001
scheduler: "cosine"

# 激进设置（需要仔细监控）
learning_rate: 0.01
scheduler: "step"
```

##### 批次大小选择

```yaml
# 小内存设备
batch_size: 4

# 标准设置
batch_size: 8

# 大内存设备
batch_size: 16
```

##### 模型复杂度控制

```yaml
# 简化模型（快速实验）
model:
  task_graph:
    hidden_dim: 256
    num_gnn_layers: 2
  prototype:
    num_prototypes_per_action: 2

# 标准模型（推荐）
model:
  task_graph:
    hidden_dim: 512
    num_gnn_layers: 3
  prototype:
    num_prototypes_per_action: 3

# 复杂模型（高性能需求）
model:
  task_graph:
    hidden_dim: 1024
    num_gnn_layers: 4
  prototype:
    num_prototypes_per_action: 5
```

### 数据预处理示例

#### 数据验证脚本

数据验证功能已集成到统一训练启动器中，运行训练脚本时会自动进行数据检查：

```bash
# 运行训练时自动进行数据验证
python scripts/train.py --profile=3060ti --amp
```

数据统计信息会保存到 `E:/Code/SCI_1/Outputs/reports/data_statistics.txt`。

#### 自定义数据加载

```python
from src.data_loader_fixed import BreakfastDataset
from torch.utils.data import DataLoader

# 创建自定义数据集
dataset = BreakfastDataset(
    root_dir="data/breakfast_data",
    views=["s1", "s2"],  # 只使用两个视角
    max_sequence_length=500,  # 较短序列
    feature_dim=65,
    is_training=True
)

# 创建数据加载器
loader = DataLoader(
    dataset,
    batch_size=4,
    shuffle=True,
    num_workers=2
)

# 检查数据
for batch in loader:
    print(f"特征形状: {batch['features'].shape}")
    print(f"标签形状: {batch['labels'].shape}")
    print(f"序列长度: {batch['sequence_length']}")
    break
```

### 模型训练示例

#### 基础训练循环

```python
import torch
import torch.optim as optim
from src.model import ProceduralVideoUnderstanding
from src.evaluation_simple import Evaluator

def train_model(config, train_loader, test_loader):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 创建模型
    model = ProceduralVideoUnderstanding(config).to(device)
    optimizer = optim.Adam(model.parameters(), lr=config['training']['learning_rate'])
    evaluator = Evaluator(config)

    best_accuracy = 0.0

    for epoch in range(config['training']['num_epochs']):
        # 训练阶段
        model.train()
        epoch_loss = 0.0

        for batch in train_loader:
            # 移动数据到设备
            for key in ['features', 'labels', 'sequence_length']:
                if key in batch:
                    batch[key] = batch[key].to(device)

            # 前向传播
            optimizer.zero_grad()
            outputs = model(batch, mode='train')
            losses = model.compute_loss(outputs, batch)
            total_loss = losses['total_loss']

            # 反向传播
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_loss += total_loss.item()

        # 评估阶段
        model.eval()
        accuracy = evaluate_model(model, test_loader, device)

        print(f"Epoch {epoch}: Loss={epoch_loss/len(train_loader):.4f}, Acc={accuracy:.4f}")

        # 保存最佳模型
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            torch.save(model.state_dict(), 'best_model.pth')

    return model, best_accuracy

def evaluate_model(model, test_loader, device):
    correct = 0
    total = 0

    with torch.no_grad():
        for batch in test_loader:
            for key in ['features', 'labels', 'sequence_length']:
                if key in batch:
                    batch[key] = batch[key].to(device)

            outputs = model(batch, mode='eval')
            predictions = outputs['action_predictions']
            labels = batch['labels']

            valid_mask = labels >= 0
            if valid_mask.sum() > 0:
                correct += (predictions[valid_mask] == labels[valid_mask]).sum().item()
                total += valid_mask.sum().item()

    return correct / total if total > 0 else 0.0
```

#### 高级训练特性

```python
# 学习率调度
scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)

# 早停机制
class EarlyStopping:
    def __init__(self, patience=10, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None

    def __call__(self, val_score):
        if self.best_score is None:
            self.best_score = val_score
        elif val_score < self.best_score + self.min_delta:
            self.counter += 1
            if self.counter >= self.patience:
                return True
        else:
            self.best_score = val_score
            self.counter = 0
        return False

# 使用早停
early_stopping = EarlyStopping(patience=15)
for epoch in range(num_epochs):
    # ... 训练代码 ...
    val_accuracy = evaluate_model(model, test_loader, device)

    if early_stopping(val_accuracy):
        print(f"Early stopping at epoch {epoch}")
        break
```

---

## 故障排除指南

### 常见问题及解决方案

#### 1. 环境和依赖问题

**问题**: ModuleNotFoundError

```bash
ModuleNotFoundError: No module named 'torch'
```

**解决方案**:

```bash
# 确认环境激活 (Linux服务器环境)
conda activate sci_1_server

# 重新安装PyTorch
conda install pytorch torchvision torchaudio -c pytorch

# 验证安装
python -c "import torch; print(torch.__version__)"
```

**问题**: CUDA相关错误

```bash
RuntimeError: CUDA out of memory
```

**解决方案**:

```python
# 1. 减小批次大小
config['training']['batch_size'] = 4

# 2. 使用CPU训练
device = torch.device('cpu')

# 3. 清理GPU缓存
torch.cuda.empty_cache()
```

#### 2. 数据加载问题

**问题**: 数据文件不存在

```bash
FileNotFoundError: data/breakfast_data/s1 not found
```

**解决方案**:

```bash
# 检查数据目录结构
ls -la data/breakfast_data/
# 应该包含: s1/, s2/, s3/, s4/
```

**问题**: 特征维度不匹配

```bash
RuntimeError: Expected input tensor to have shape [*, 65] but got [*, 64]
```

**解决方案**:

```yaml
# 检查并更新配置文件中的特征维度
data:
  feature_dim: 64  # 根据实际数据调整
```

#### 3. 模型训练问题

**问题**: 损失不收敛

```python
# 现象：损失值持续很高或震荡
Epoch 10: Loss=5.2341, Acc=0.2000
Epoch 20: Loss=5.1892, Acc=0.2100
```

**解决方案**:

```yaml
# 1. 降低学习率
training:
  learning_rate: 0.0001  # 从0.001降低

# 2. 调整损失权重
training:
  loss_weights:
    action_classification: 1.0
    prototype_contrastive: 0.1  # 降低
    task_graph_structure: 0.1   # 降低
```

**问题**: 梯度爆炸

```bash
RuntimeError: Function 'AddBackward0' returned nan values in its 0th output.
```

**解决方案**:

```python
# 添加梯度裁剪
torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

# 检查学习率
if optimizer.param_groups[0]['lr'] > 0.01:
    print("学习率过高，建议降低到0.001以下")
```

#### 4. 内存问题

**问题**: 内存不足

```bash
RuntimeError: [enforce fail at alloc_cpu.cpp:75]
```

**解决方案**:

```python
# 1. 减少序列长度
config['data']['max_sequence_length'] = 500

# 2. 减少批次大小
config['training']['batch_size'] = 2

# 3. 减少模型复杂度
config['model']['task_graph']['hidden_dim'] = 256
config['model']['task_graph']['num_gnn_layers'] = 2
```

#### 5. 可视化问题

**问题**: 中文字体显示问题

```python
# 现象：图表中中文显示为方框
```

**解决方案**:

```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 或者使用英文标题
plt.title('Training Loss', fontsize=14)  # 替代中文标题
```

### 性能优化建议

#### 1. 训练速度优化

```python
# 使用更多CPU核心
train_loader = DataLoader(
    dataset,
    batch_size=config['training']['batch_size'],
    shuffle=True,
    num_workers=4,  # 增加worker数量
    pin_memory=True  # 启用内存固定
)

# 使用混合精度训练（如果支持GPU）
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()
with autocast():
    outputs = model(batch, mode='train')
    losses = model.compute_loss(outputs, batch)

scaler.scale(losses['total_loss']).backward()
scaler.step(optimizer)
scaler.update()
```

#### 2. 内存使用优化

```python
# 定期清理缓存
if epoch % 10 == 0:
    torch.cuda.empty_cache()

# 使用检查点技术
from torch.utils.checkpoint import checkpoint

def forward_with_checkpoint(self, x):
    return checkpoint(self.some_expensive_function, x)
```

#### 3. 模型性能优化

```python
# 模型量化（推理时）
model_quantized = torch.quantization.quantize_dynamic(
    model, {torch.nn.Linear}, dtype=torch.qint8
)

# 模型剪枝
import torch.nn.utils.prune as prune

for module in model.modules():
    if isinstance(module, torch.nn.Linear):
        prune.l1_unstructured(module, name='weight', amount=0.2)
```

### 调试技巧

#### 1. 逐步调试

```python
# 检查数据流
def debug_data_flow(model, batch, device):
    batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v
             for k, v in batch.items()}

    print(f"输入特征形状: {batch['features'].shape}")
    print(f"输入标签形状: {batch['labels'].shape}")

    # 逐步前向传播
    with torch.no_grad():
        # 1. 多视角融合
        fusion_outputs = model.multi_view_fusion([batch['features']], [0])
        print(f"融合特征形状: {fusion_outputs['fused_features'].shape}")

        # 2. 时序编码
        temporal_features, _ = model.temporal_encoder(fusion_outputs['fused_features'])
        print(f"时序特征形状: {temporal_features.shape}")

        # 3. 动作分类
        action_logits = model.action_classifier(temporal_features)
        print(f"动作logits形状: {action_logits.shape}")

# 使用示例
debug_data_flow(model, next(iter(train_loader)), device)
```

#### 2. 损失分析

```python
def analyze_losses(model, batch, device):
    outputs = model(batch, mode='train')
    losses = model.compute_loss(outputs, batch)

    print("各项损失分析:")
    for loss_name, loss_value in losses.items():
        print(f"  {loss_name}: {loss_value.item():.6f}")

    # 检查梯度
    model.zero_grad()
    losses['total_loss'].backward()

    total_norm = 0
    for p in model.parameters():
        if p.grad is not None:
            param_norm = p.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
    total_norm = total_norm ** (1. / 2)

    print(f"梯度范数: {total_norm:.6f}")
```

#### 3. 模型状态检查

```python
def check_model_state(model):
    print("模型参数统计:")
    total_params = 0
    trainable_params = 0

    for name, param in model.named_parameters():
        total_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()

        # 检查参数范围
        param_min = param.data.min().item()
        param_max = param.data.max().item()
        param_mean = param.data.mean().item()

        print(f"  {name}: {param.shape}, 范围[{param_min:.4f}, {param_max:.4f}], 均值{param_mean:.4f}")

    print(f"总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")
```

---

## 详细技术报告模板

#### 原型学习和静态任务图训练系统 - 技术报告

**报告类型**: [实验报告/性能分析/技术总结]
**生成时间**: YYYY年MM月DD日 HH:MM:SS
**实验编号**: EXP_YYYYMMDD_XXX

##### 执行摘要

**项目目标**:
实现基于原型学习和静态任务图的程序性视频理解系统，在breakfast数据集上验证方法有效性。

**主要成果**:

- ✅ 成功构建了完整的训练和评估框架
- ✅ 实现了XX.XX%的帧级别准确率
- ✅ 验证了原型学习在动作识别中的有效性
- ✅ 建立了静态任务图学习机制

**技术亮点**:

- 简化架构设计，避免复杂依赖
- 模块化实现，便于扩展和维护
- 全面评估体系，支持多维度分析
- 详细文档和故障排除指南

##### 实验设计

**数据集配置**:

```yaml
数据集: Breakfast Actions (cereals子集)
训练视角: s1, s2, s3 (XXX个样本)
测试视角: s4 (XXX个样本)
动作类别: 5个 (SIL, pour_cereals, pour_milk, stir_cereals, take_bowl)
特征维度: 65
最大序列长度: 1000帧
```

**模型架构**:

```yaml
多视角融合:
  - 方法: concat/mean
  - 编码器维度: 256
  - 输出维度: 512

时序编码器:
  - 类型: 双向LSTM
  - 隐藏层维度: 256
  - 层数: 2

任务图学习:
  - GNN层数: 3
  - 隐藏维度: 512
  - 图池化: mean

原型学习:
  - 原型维度: 256
  - 每动作原型数: 3
  - 温度参数: 0.1
  - 动量系数: 0.9
```

**训练配置**:

```yaml
优化器: Adam
学习率: 0.001
批次大小: 8
训练轮数: 100
权重衰减: 0.0001
学习率调度: cosine
梯度裁剪: 1.0

损失权重:
  - 动作分类: 1.0
  - 原型对比: 0.5
  - 任务图结构: 0.3
  - 时序一致性: 0.2
  - 视角对齐: 0.1
```

##### 实验结果

**整体性能**:

```text
最佳测试准确率: XX.XX%
最终训练损失: X.XXXX
收敛轮数: XX轮
训练总时间: XX小时XX分钟
模型参数数: XXX万
```

**详细指标**:

```text
帧级别准确率: XX.XX%
段级别准确率: XX.XX%
宏平均精确率: XX.XX%
宏平均召回率: XX.XX%
宏平均F1分数: XX.XX%
编辑距离: XX.XX
```

**各类别性能**:

```text
SIL: 精确率XX.XX%, 召回率XX.XX%, F1分数XX.XX%
pour_cereals: 精确率XX.XX%, 召回率XX.XX%, F1分数XX.XX%
pour_milk: 精确率XX.XX%, 召回率XX.XX%, F1分数XX.XX%
stir_cereals: 精确率XX.XX%, 召回率XX.XX%, F1分数XX.XX%
take_bowl: 精确率XX.XX%, 召回率XX.XX%, F1分数XX.XX%
```

**原型质量评估**:

```text
平均原型内聚度: XX.XX
平均原型分离度: XX.XX
原型更新稳定性: XX.XX%
原型覆盖率: XX.XX%
```

**任务图分析**:

```text
学习到的边数: XX条
图稀疏度: XX.XX%
DAG约束满足率: XX.XX%
时序一致性得分: XX.XX
```

##### 消融实验

**融合方法对比**:

```text
Concat融合: 准确率XX.XX%
Mean融合: 准确率XX.XX%
最佳方法: [concat/mean]
```

**损失权重敏感性**:

```text
原型损失权重0.1: 准确率XX.XX%
原型损失权重0.5: 准确率XX.XX%
原型损失权重1.0: 准确率XX.XX%
最佳权重: X.X
```

**模型复杂度影响**:

```text
隐藏维度256: 准确率XX.XX%, 参数XXX万
隐藏维度512: 准确率XX.XX%, 参数XXX万
隐藏维度1024: 准确率XX.XX%, 参数XXX万
最佳配置: XXX维度
```

##### 错误分析

**混淆矩阵分析**:

- 最容易混淆的动作对: [动作A] ↔ [动作B] (XX.XX%错误率)
- 识别最困难的动作: [动作名] (XX.XX%准确率)
- 识别最容易的动作: [动作名] (XX.XX%准确率)

**时序错误分析**:

- 动作边界检测准确率: XX.XX%
- 平均时序偏移: XX.XX帧
- 长序列vs短序列性能差异: XX.XX%

**视角泛化分析**:

- 训练视角内准确率: XX.XX%
- 跨视角泛化准确率: XX.XX%
- 视角敏感性得分: XX.XX

##### 计算资源分析

**训练资源消耗**:

```text
CPU使用率: 平均XX%, 峰值XX%
内存使用: 平均XXG, 峰值XXG
GPU使用率: 平均XX%, 峰值XX% (如适用)
磁盘I/O: 读取XXG, 写入XXG
```

**推理性能**:

```text
单样本推理时间: XX.XX毫秒
批次推理时间: XX.XX秒 (批次大小XX)
内存占用: XXX MB
模型文件大小: XXX MB
```

##### 结论和建议

**主要发现**:

1. 原型学习有效提升了特征区分度
2. 静态任务图成功建模了动作依赖关系
3. 简化融合方法在保持性能的同时降低了复杂度
4. 模块化设计便于后续扩展和优化

**技术优势**:

- 无需复杂注意力机制，训练稳定
- 不依赖外部图神经网络库，部署简单
- 支持多视角数据，泛化能力强
- 提供完整评估体系，便于分析

**改进方向**:

1. **数据增强**: 增加更多动作类别和视角数据
2. **模型优化**: 尝试更先进的图神经网络架构
3. **损失函数**: 设计更有效的多任务损失平衡策略
4. **推理加速**: 实现模型量化和剪枝优化

**应用前景**:

- 可扩展到更大规模的动作识别数据集
- 为动态任务图学习提供稳定基础
- 适用于机器人学习和人机交互场景
- 支持实时视频理解应用

---

### 使用说明

#### 如何使用报告模板

1. **选择合适的模板**:
   - 详细技术报告: 适用于完整实验总结

2. **填写模板内容**:
   - 将模板中的占位符 (如XX.XX%, XXX等) 替换为实际数值
   - 根据实际情况调整章节内容
   - 添加具体的分析和见解

3. **生成可视化**:
   - 运行 `scripts/run_complete_experiment.py` 自动生成基础图表
   - 手动创建特定的可视化图表

4. **定期更新**:
   - 重要实验后更新技术报告
   - 保持文档与代码同步

---

## 总结

本技术文档提供了原型学习和静态任务图训练系统的完整技术指南，包括：

1. **系统概述**: 核心架构和技术特点
2. **任务图设计**: 详细的实现原理和训练机制
3. **评估体系**: 全面的性能评估方法和指标
4. **代码示例**: 实用的配置和使用指南
5. **故障排除**: 常见问题的解决方案
6. **报告模板**: 标准化的技术报告格式

该文档旨在为开发者和研究人员提供：

- 深入理解系统设计理念
- 快速上手和使用指导
- 问题诊断和性能优化建议
- 标准化的实验报告格式

通过遵循本文档的指导，用户可以高效地使用和扩展该系统，为程序性视频理解研究做出贡献。
