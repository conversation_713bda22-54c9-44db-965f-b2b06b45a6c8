"""
中文字体配置模块
解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
# import platform  # 移除平台检查，Linux专用
import os
import warnings


def setup_chinese_font():
    """
    设置matplotlib中文字体支持 - Linux专用
    自动检测Linux系统中可用的中文字体并配置
    """
    try:
        # Linux中文字体列表（按优先级排序）
        chinese_fonts = [
            'WenQuanYi Micro Hei',  # 文泉驿微米黑
            'WenQuanYi Zen Hei',    # 文泉驿正黑
            'Noto Sans CJK SC',     # 思源黑体
            'Source Han Sans CN',   # 思源黑体
            'DejaVu Sans',          # DejaVu Sans
            'Liberation Sans',      # Liberation Sans
            'Droid Sans Fallback'   # Droid Sans Fallback
        ]
        
        # 获取系统中所有可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        # 查找第一个可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break
        
        if selected_font:
            # 设置matplotlib字体
            plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            
            print(f"✅ 中文字体设置成功: {selected_font}")
            return selected_font
        else:
            # 如果没有找到中文字体，尝试使用系统默认字体
            warnings.warn("未找到合适的中文字体，将使用系统默认字体")
            plt.rcParams['axes.unicode_minus'] = False
            return None
            
    except Exception as e:
        warnings.warn(f"设置中文字体时出错: {e}")
        return None


def test_chinese_display():
    """
    测试中文字体显示效果
    """
    import numpy as np
    
    # 设置中文字体
    font_name = setup_chinese_font()
    
    # 创建测试图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 测试图表1：简单折线图
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax1.plot(x, y, 'b-', linewidth=2, label='正弦曲线')
    ax1.set_title('中文标题测试', fontsize=14, fontweight='bold')
    ax1.set_xlabel('横轴标签')
    ax1.set_ylabel('纵轴标签')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 测试图表2：柱状图
    categories = ['训练损失', '验证损失', '测试准确率', '训练准确率']
    values = [0.5, 0.6, 0.85, 0.92]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    bars = ax2.bar(categories, values, color=colors, alpha=0.8)
    ax2.set_title('性能指标对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('数值')
    ax2.set_ylim(0, 1)
    
    # 在柱子上添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存测试图片
    try:
        from picture_utils import save_figure
        save_path = save_figure("chinese_font_test.png", fig=fig)
    except ImportError:
        # 如果picture_utils不可用，直接保存到当前目录
        save_path = "chinese_font_test.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.close()
    
    print(f"✅ 中文字体测试图片已保存: {save_path}")
    
    if font_name:
        print(f"✅ 使用字体: {font_name}")
    else:
        print("⚠️ 使用系统默认字体")
    
    return save_path


def get_available_chinese_fonts():
    """
    获取系统中所有可用的中文字体
    """
    available_fonts = []
    
    # Linux中文字体关键词
    chinese_keywords = [
        'WenQuanYi', 'Noto', 'Source Han', 'Droid', 'DejaVu', 'Liberation'
    ]
    
    # 获取所有字体
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 筛选可能的中文字体
    for font in all_fonts:
        for keyword in chinese_keywords:
            if keyword.lower() in font.lower():
                if font not in available_fonts:
                    available_fonts.append(font)
                break
    
    return sorted(available_fonts)


def print_font_info():
    """
    打印字体信息 - Linux专用
    """
    print("🔤 字体配置信息:")
    print("   系统: Linux")

    available_fonts = get_available_chinese_fonts()
    print(f"   可用中文字体数量: {len(available_fonts)}")

    if available_fonts:
        print("   可用中文字体:")
        for i, font in enumerate(available_fonts[:10]):  # 只显示前10个
            print(f"     {i+1}. {font}")
        if len(available_fonts) > 10:
            print(f"     ... 还有 {len(available_fonts) - 10} 个字体")
    else:
        print("   ⚠️ 未找到中文字体")

    # 显示当前matplotlib字体设置
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"   当前matplotlib字体: {current_font}")


if __name__ == "__main__":
    # 测试字体配置
    print("🚀 测试中文字体配置...")
    
    # 打印字体信息
    print_font_info()
    
    # 设置中文字体
    setup_chinese_font()
    
    # 测试中文显示
    test_chinese_display()
    
    print("✅ 中文字体配置测试完成")
