#!/usr/bin/env python3
"""
静态模型训练脚本 - 专为双A6000硬件配置优化
支持Linux服务器环境下的高性能训练

输出路径：
- 模型参数：/data2/syd_data/Breakfast_Data/Outputs/checkpoints/static_model_a6000.pth
- 训练报告：/data2/syd_data/Breakfast_Data/Outputs/reports/static_training_report_*.md
- 可视化图表：/data2/syd_data/Breakfast_Data/Outputs/visualizations/static_training_*.png
"""

import argparse
import os
import sys
import yaml
# import platform  # 移除平台检查，Linux专用
from pathlib import Path
from typing import Dict, Optional, Any

# PyTorch相关导入 - 修复版本兼容性问题
try:
    import torch
    # 启用 torch.compile 时捕获 scalar outputs，避免 .item() 中断图
    torch._dynamo.config.capture_scalar_outputs = True
    import torch.distributed as dist
    import torch.nn as nn

    # 修复混合精度训练导入问题 - 优先使用新API
    try:
        # 尝试新的API (PyTorch 2.0+) - 这是当前环境支持的
        from torch.amp import GradScaler, autocast
        AMP_NEW_API = True
        print(f"✅ PyTorch {torch.__version__} - 使用新的混合精度API")
    except ImportError:
        try:
            # 回退到旧的API (PyTorch 1.x)
            from torch.cuda.amp import GradScaler, autocast
            AMP_NEW_API = False
            print(f"✅ PyTorch {torch.__version__} - 使用旧的混合精度API")
        except ImportError:
            # 如果都不可用，禁用混合精度
            GradScaler = None
            autocast = None
            AMP_NEW_API = False
            print("⚠️ 混合精度训练不可用，将使用标准精度")

    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")
    TORCH_AVAILABLE = False
    GradScaler = None
    autocast = None
    AMP_NEW_API = False

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 内存分配优化：开启可扩展分段以减少碎片
os.environ.setdefault("PYTORCH_CUDA_ALLOC_CONF", "expandable_segments:True")
# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置 - Linux服务器环境
def get_output_dir():
    """Linux服务器环境输出目录"""
    return Path("/data2/syd_data/Breakfast_Data/Outputs")

OUTPUT_DIR = get_output_dir()
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
VIS_DIR = OUTPUT_DIR / "visualizations"
REPORT_DIR = OUTPUT_DIR / "reports"


def parse_cli() -> argparse.Namespace:
    """解析命令行参数 - A6000专用"""
    # 导入通用训练工具
    from src.training_utils import parse_training_cli
    return parse_training_cli("静态模型训练启动器 - 双A6000硬件配置")


# 使用通用训练工具中的函数
from src.training_utils import (
    validate_platform_compatibility,
    init_device_and_distributed,
    setup_hardware_optimization,
    create_optimizer,
    setup_model_compilation,
    setup_distributed_model,
    cleanup_gpu_memory,
    create_mixed_precision_scaler,
    set_random_seed,
    worker_init_fn
)


# 硬件优化函数已移至 training_utils.py


def load_and_update_config(config_path: str, optimization_config: Dict[str, Any]) -> Dict:
    """加载并更新配置文件"""
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    # 应用硬件优化配置
    if optimization_config["device_type"] == "cuda":
        config["training"]["batch_size"] = optimization_config["batch_size"]
        config["training"]["num_workers"] = optimization_config["num_workers"]
        
        # 更新GPU优化配置
        gpu_opt = config["training"].setdefault("gpu_optimization", {})
        gpu_opt.update({
            "enable_amp": True,
            "num_workers": optimization_config["num_workers"],
            "pin_memory": True,
            "max_memory_fraction": optimization_config["memory_fraction"],
            "memory_cleanup_freq": optimization_config["cleanup_freq"],
            "enable_tf32": optimization_config.get("enable_tf32", False),
            "enable_compile": optimization_config.get("enable_compile", False),
            "persistent_workers": True,
            "prefetch_factor": 4,
        })
        
        print(f"✅ 配置已更新为{optimization_config['profile']}优化")
        print(f"   - 批处理大小: {config['training']['batch_size']}")
        print(f"   - 数据加载器worker: {config['training']['num_workers']}")
        print(f"   - 显存使用比例: {optimization_config['memory_fraction']*100:.0f}%")
    
    return config


# 优化器、模型编译、分布式等函数已移至 training_utils.py


def train_epoch(model: nn.Module, train_loader, optimizer: torch.optim.Optimizer,
                scaler: Optional[GradScaler], device: torch.device,
                optimization_config: Dict, args: argparse.Namespace) -> Dict[str, float]:
    """训练一个epoch"""
    model.train()
    epoch_metrics = {"loss": 0.0, "correct": 0, "total": 0}

    cleanup_freq = optimization_config.get("cleanup_freq", 300)
    accum_steps = max(1, args.accum)

    for batch_idx, batch in enumerate(train_loader):
        # 移动并优化批次内存格式
        _move_batch_to_device(batch, device, optimization_config)

        # 梯度清零 - 修复：在累积周期开始时清零梯度
        if batch_idx % accum_steps == 0:
            optimizer.zero_grad(set_to_none=True)

        # 前向传播和反向传播
        total_loss, outputs = _forward_backward_pass(
            model, batch, args, scaler, accum_steps
        )

        # 优化器步骤 - 在累积周期结束时执行
        if (batch_idx + 1) % accum_steps == 0:
            _optimizer_step(optimizer, scaler, args)

        # 更新统计信息
        _update_epoch_metrics(epoch_metrics, total_loss, outputs, batch, accum_steps)

        # 定期清理和显示进度
        _periodic_maintenance(batch_idx, cleanup_freq, optimization_config, epoch_metrics)

    return {
        "loss": epoch_metrics["loss"] / len(train_loader),
        "accuracy": epoch_metrics["correct"] / epoch_metrics["total"] if epoch_metrics["total"] > 0 else 0
    }

def _move_batch_to_device(batch: Dict, device: torch.device, optimization_config: Dict) -> None:
    """移动批次数据到设备，并根据配置调整内存格式"""
    for key in ["features", "labels"]:
        if key in batch:
            tensor = batch[key].to(device, non_blocking=True)
            if key == "features" and optimization_config.get("enable_channels_last", False) and tensor.ndim == 4:
                tensor = tensor.to(memory_format=torch.channels_last)
            batch[key] = tensor

def _forward_backward_pass(model, batch, args, scaler, accum_steps):
    """执行前向和反向传播"""
    if args.amp and scaler is not None:
        return _amp_forward_backward(model, batch, scaler, accum_steps)
    else:
        return _standard_forward_backward(model, batch, accum_steps)

def _amp_forward_backward(model, batch, scaler, accum_steps):
    """混合精度前向反向传播 - 修复版本兼容性"""
    if autocast is None:
        # 如果autocast不可用，使用标准前向传播
        return _standard_forward_backward(model, batch, accum_steps)

    try:
        # 使用可用的autocast API
        if AMP_NEW_API:
            # 新API: torch.amp.autocast
            with autocast('cuda'):
                outputs = model(batch, mode="train")
                losses = model.compute_loss(outputs, batch)
                total_loss = losses["total_loss"] / accum_steps
        else:
            # 旧API: torch.cuda.amp.autocast
            with autocast():
                outputs = model(batch, mode="train")
                losses = model.compute_loss(outputs, batch)
                total_loss = losses["total_loss"] / accum_steps
    except Exception as e:
        print(f"⚠️ 混合精度前向传播失败，回退到标准精度: {e}")
        return _standard_forward_backward(model, batch, accum_steps)

    scaler.scale(total_loss).backward()
    return total_loss, outputs

def _standard_forward_backward(model, batch, accum_steps):
    """标准前向反向传播"""
    outputs = model(batch, mode="train")
    losses = model.compute_loss(outputs, batch)
    total_loss = losses["total_loss"] / accum_steps
    total_loss.backward()
    return total_loss, outputs

def _optimizer_step(optimizer, scaler, args):
    """执行优化器步骤"""
    if args.amp and scaler is not None:
        scaler.step(optimizer)
        scaler.update()
    else:
        optimizer.step()

def _update_epoch_metrics(epoch_metrics, total_loss, outputs, batch, accum_steps):
    """更新epoch统计信息"""
    epoch_metrics["loss"] += total_loss.item() * accum_steps
    predictions = torch.argmax(outputs["action_logits"], dim=-1)
    labels = batch["labels"]
    # 只统计有效标签帧 (标签>=0)
    valid_mask = labels >= 0
    epoch_metrics["correct"] += (predictions[valid_mask] == labels[valid_mask]).sum().item()
    epoch_metrics["total"] += valid_mask.sum().item()

def _periodic_maintenance(batch_idx, cleanup_freq, optimization_config, epoch_metrics):
    """定期维护：内存清理和进度显示"""
    if batch_idx % cleanup_freq == 0:
        cleanup_gpu_memory(optimization_config)

    if batch_idx % 20 == 0:
        current_acc = epoch_metrics["correct"] / epoch_metrics["total"] if epoch_metrics["total"] > 0 else 0
        print(f"   Batch {batch_idx}: Acc={current_acc:.4f}")


def generate_training_report(train_history: Dict, config: Dict, optimization_config: Dict,
                           best_accuracy: float, model_path: Path, profile: str) -> None:
    """
    生成详细的训练报告

    Args:
        train_history: 训练历史记录
        config: 训练配置
        optimization_config: 硬件优化配置
        best_accuracy: 最佳准确率
        model_path: 模型保存路径
        profile: 硬件配置文件
    """
    try:
        # 动态导入可视化模块 - 优雅处理可选依赖
        import matplotlib.pyplot as plt
        import json
        from datetime import datetime

        # 导入中文字体配置
        from src.font_config import setup_chinese_font
        setup_chinese_font()

        # 导入图片保存工具
        from src.picture_utils import save_figure

        # 创建训练曲线图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        epochs = train_history["epoch"]

        # 1. 训练损失曲线
        ax1.plot(epochs, train_history["train_loss"], 'b-', label='训练损失', linewidth=2)
        ax1.set_title('训练损失曲线', fontsize=14, fontweight='bold')
        ax1.set_xlabel('训练轮数')
        ax1.set_ylabel('损失值')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 2. 准确率曲线
        ax2.plot(epochs, train_history["train_accuracy"], 'g-', label='训练准确率', linewidth=2)
        ax2.plot(epochs, train_history["test_accuracy"], 'r-', label='测试准确率', linewidth=2)
        ax2.set_title('准确率曲线', fontsize=14, fontweight='bold')
        ax2.set_xlabel('训练轮数')
        ax2.set_ylabel('准确率')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 3. 性能提升趋势
        if len(train_history["test_accuracy"]) > 1:
            improvement = [train_history["test_accuracy"][i] - train_history["test_accuracy"][0]
                          for i in range(len(train_history["test_accuracy"]))]
            ax3.plot(epochs, improvement, 'purple', linewidth=2, marker='o')
            ax3.set_title('性能提升趋势', fontsize=14, fontweight='bold')
            ax3.set_xlabel('训练轮数')
            ax3.set_ylabel('准确率提升')
            ax3.grid(True, alpha=0.3)
            ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

        # 4. 硬件配置信息
        ax4.axis('off')
        config_text = f"""硬件配置信息 ({profile.upper()})

批处理大小: {config['training']['batch_size']}
学习率: {config['training']['learning_rate']:.6f}
训练轮数: {config['training']['num_epochs']}
最佳准确率: {best_accuracy:.4f}

GPU优化:
• 混合精度训练: {'启用' if optimization_config.get('enable_amp', False) else '禁用'}
• 显存使用比例: {optimization_config.get('memory_fraction', 0.9)*100:.0f}%
• 数据加载器Worker: {optimization_config.get('num_workers', 4)}
• 内存清理频率: {optimization_config.get('cleanup_freq', 300)}批次

平台信息:
• 操作系统: {optimization_config.get('platform', 'Unknown')}
• 设备类型: {optimization_config.get('device_type', 'Unknown')}
• TF32加速: {'启用' if optimization_config.get('enable_tf32', False) else '禁用'}
"""
        ax4.text(0.05, 0.95, config_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

        plt.tight_layout()

        # 保存训练曲线图
        curves_path = save_figure(f"training_curves_{profile}.png", fig=fig)
        plt.close()


        # Delegate report generation to English-only module
        from src.report_generator import TrainingReportGenerator

        report_gen = TrainingReportGenerator(model_type='static')
        report_path = report_gen.generate_markdown_report(
            train_history, config, optimization_config, best_accuracy, model_path, profile
        )
        print(f"✅ Training report generated: {report_path}")

        # 保存JSON格式的训练历史
        history_path = REPORT_DIR / f"static_training_history_{profile}.json"
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump({
                'train_history': train_history,
                'config': config,
                'optimization_config': optimization_config,
                'best_accuracy': best_accuracy,
                'model_path': str(model_path),
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)

        print(f"✅ 训练报告已生成:")
        print(f"   📄 详细报告: {report_path}")
        print(f"   📊 训练曲线: {curves_path}")
        print(f"   📋 历史数据: {history_path}")

    except Exception as e:
        print(f"⚠️ 报告生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 统一训练启动器")
    print("=" * 60)

    # 解析命令行参数
    args = parse_cli()
    print(f"📋 硬件配置: {args.profile}")
    print(f"📋 平台: Linux")

    try:
        # 创建输出目录
        for directory in [CHECKPOINT_DIR, VIS_DIR, REPORT_DIR]:
            directory.mkdir(parents=True, exist_ok=True)

        # 初始化设备和分布式
        device = init_device_and_distributed(args.profile)

        # 设置硬件优化
        optimization_config = setup_hardware_optimization(args.profile, device)

        # 加载配置
        config = load_and_update_config(args.config, optimization_config)

        # 设置随机种子确保实验可复现性
        seed = config.get("experiment", {}).get("seed", 42)
        set_random_seed(seed, deterministic=True)

        # 导入训练模块
        print("\n📦 导入训练模块...")
        from src.data_loader_fixed import create_data_loaders
        from src.model import ProceduralVideoUnderstanding
        from src.evaluation_simple import Evaluator
        print("✅ 所有训练模块导入成功")

        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        train_loader, test_loader = create_data_loaders(config, worker_init_fn=worker_init_fn)

        # 根据数据集更新动作类别数，防止配置不一致导致索引错误
        num_actions = len(train_loader.dataset.action_to_id)
        config["data"]["num_actions"] = num_actions
        print(f"✅ 动作类别数更新为: {num_actions}")

        # 创建模型
        print("🏗️ 创建模型...")
        model = ProceduralVideoUnderstanding(config).to(device)
        # channels_last 内存格式，以减少内存占用
        if optimization_config.get("enable_channels_last", False):
            model = model.to(memory_format=torch.channels_last)
            print("✅ 模型已转换为 channels_last 内存格式")

        # 模型编译优化
        model = setup_model_compilation(model, args, optimization_config)
        if args.compile:
            try:
                import torch._dynamo
                torch._dynamo.config.suppress_errors = True
                print("✅ 已启用 torch._dynamo 错误抑制 (编译/运行时错误将回退至Eager模式)")
            except ImportError:
                pass

        # 分布式模型包装
        model = setup_distributed_model(model, optimization_config)

        # 创建优化器
        optimizer = create_optimizer(model, config, optimization_config)

        # 创建学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=config["training"]["num_epochs"]
        )

        # 混合精度训练 - 修复版本兼容性
        scaler = None
        if args.amp and device.type == "cuda" and GradScaler is not None:
            try:
                # 统一使用标准的GradScaler初始化
                scaler = GradScaler()
                if AMP_NEW_API:
                    print("✅ 启用混合精度训练 (新API兼容)")
                else:
                    print("✅ 启用混合精度训练 (旧API)")
            except Exception as e:
                print(f"⚠️ 混合精度训练初始化失败: {e}")
                scaler = None
        elif args.amp:
            print("⚠️ 混合精度训练不可用，使用标准精度")

        # 创建评估器
        evaluator = Evaluator(config)

        print(f"\n🎯 开始训练 ({args.profile}配置)")
        print(f"   批处理大小: {config['training']['batch_size']}")
        print(f"   训练轮数: {config['training']['num_epochs']}")

        # 训练循环
        best_accuracy = 0.0
        train_history = {"epoch": [], "train_loss": [], "train_accuracy": [], "test_accuracy": []}

        for epoch in range(config["training"]["num_epochs"]):
            print(f"\n📈 Epoch {epoch+1}/{config['training']['num_epochs']}")

            # 为分布式训练设置epoch（确保数据混洗的随机性）
            if hasattr(train_loader, 'sampler') and hasattr(train_loader.sampler, 'set_epoch'):
                train_loader.sampler.set_epoch(epoch)
                print(f"🔄 已为分布式采样器设置epoch: {epoch}")

            # 训练
            train_metrics = train_epoch(model, train_loader, optimizer, scaler,
                                      device, optimization_config, args)

            # 评估
            model.eval()
            with torch.no_grad():
                test_results = evaluator.evaluate(model, test_loader, device)

            # 更新学习率
            scheduler.step()

            # 记录历史
            train_history["epoch"].append(epoch + 1)
            train_history["train_loss"].append(train_metrics["loss"])
            train_history["train_accuracy"].append(train_metrics["accuracy"])
            train_history["test_accuracy"].append(test_results["frame_accuracy"])

            print(f"   训练损失: {train_metrics['loss']:.4f}")
            print(f"   训练准确率: {train_metrics['accuracy']:.4f}")
            print(f"   测试准确率: {test_results['frame_accuracy']:.4f}")

            # 保存最佳模型
            if test_results["frame_accuracy"] > best_accuracy:
                best_accuracy = test_results["frame_accuracy"]
                checkpoint_path = CHECKPOINT_DIR / f"static_model_{args.profile}.pth"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': best_accuracy,
                    'config': config,
                    'optimization_config': optimization_config
                }, checkpoint_path)
                print(f"   💾 保存最佳模型 (准确率: {best_accuracy:.4f})")

        print(f"\n🎉 训练完成！")
        print(f"   最佳准确率: {best_accuracy:.4f}")
        print(f"   模型保存位置: {CHECKPOINT_DIR}")

        # 生成详细的训练报告
        print("\n📊 生成训练报告...")
        try:
            generate_training_report(
                train_history=train_history,
                config=config,
                optimization_config=optimization_config,
                best_accuracy=best_accuracy,
                model_path=CHECKPOINT_DIR / f"static_model_{args.profile}.pth",
                profile=args.profile
            )
        except Exception as report_error:
            print(f"⚠️ 报告生成失败: {report_error}")
            print("训练已完成，但报告生成出现问题")

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)



