"""
简化的静态任务图学习模块
不依赖torch_geometric，使用基础的图神经网络实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple
import networkx as nx


class SimpleGCNLayer(nn.Module):
    """简化的图卷积层"""

    def __init__(self, in_dim: int, out_dim: int):
        super().__init__()
        self.linear = nn.Linear(in_dim, out_dim)

    def forward(self, x: torch.Tensor, adj_matrix: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 节点特征 [num_nodes, in_dim]
            adj_matrix: 邻接矩阵 [num_nodes, num_nodes]
        """
        # 简单的图卷积：AXW
        support = self.linear(x)  # [num_nodes, out_dim]
        output = torch.mm(adj_matrix, support)  # [num_nodes, out_dim]
        return F.relu(output)


class TaskGraphEncoder(nn.Module):
    """静态任务图编码器（简化版本）"""

    def __init__(
        self,
        num_actions: int,
        hidden_dim: int = 512,
        num_gnn_layers: int = 3,
        dropout: float = 0.1,
        graph_pooling: str = "mean",
    ):
        """
        Args:
            num_actions: 动作类别数量
            hidden_dim: 隐藏层维度
            num_gnn_layers: GNN层数
            dropout: Dropout概率
            graph_pooling: 图池化方法 ("mean", "max", "sum")
        """
        super().__init__()

        self.num_actions = num_actions
        self.hidden_dim = hidden_dim
        self.num_gnn_layers = num_gnn_layers
        self.graph_pooling = graph_pooling

        # 动作嵌入层
        self.action_embedding = nn.Embedding(num_actions, hidden_dim)

        # 图神经网络层
        self.gnn_layers = nn.ModuleList()
        for i in range(num_gnn_layers):
            self.gnn_layers.append(SimpleGCNLayer(hidden_dim, hidden_dim))

        # Dropout层
        self.dropout = nn.Dropout(dropout)

        # 边预测网络
        self.edge_predictor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid(),
        )

    def forward(
        self, action_sequences: torch.Tensor, sequence_lengths: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        Args:
            action_sequences: 动作序列 [batch_size, max_seq_len]
            sequence_lengths: 实际序列长度 [batch_size]

        Returns:
            包含图嵌入和边概率的字典
        """
        batch_size = action_sequences.shape[0]
        device = action_sequences.device

        # 为每个序列创建图表示
        graph_embeddings = []
        edge_probabilities = []
        node_embeddings_list = []

        for i in range(batch_size):
            seq_len = sequence_lengths[i].item()
            actions = action_sequences[i, :seq_len]
            # 过滤无效标签 (-1)
            actions = actions[actions >= 0]

            # 获取唯一动作
            unique_actions = torch.unique(actions)
            num_nodes = len(unique_actions)

            if num_nodes == 0:
                # 处理空序列
                graph_emb = torch.zeros(self.hidden_dim, device=device)
                edge_prob = torch.zeros(
                    self.num_actions, self.num_actions, device=device
                )
                node_emb = torch.zeros((0, self.hidden_dim), device=device)
            else:
                # 创建节点特征
                node_features = self.action_embedding(
                    unique_actions
                )  # [num_nodes, hidden_dim]

                # 创建邻接矩阵（全连接图）
                adj_matrix = torch.ones(num_nodes, num_nodes, device=device)
                adj_matrix = adj_matrix / num_nodes  # 归一化

                # 通过GNN层
                x = node_features
                for gnn_layer in self.gnn_layers:
                    x = gnn_layer(x, adj_matrix)
                    x = self.dropout(x)

                # 图级别池化
                if self.graph_pooling == "mean":
                    graph_emb = torch.mean(x, dim=0)
                elif self.graph_pooling == "max":
                    graph_emb = torch.max(x, dim=0)[0]
                else:  # sum
                    graph_emb = torch.sum(x, dim=0)

                # 预测边概率
                edge_prob = self._predict_edge_probabilities(x, unique_actions)
                node_emb = x

            graph_embeddings.append(graph_emb)
            edge_probabilities.append(edge_prob)
            node_embeddings_list.append(node_emb)

        # 堆叠结果
        graph_embeddings = torch.stack(graph_embeddings)  # [batch_size, hidden_dim]

        return {
            "graph_embeddings": graph_embeddings,
            # 每个样本的边概率列表
            "edge_probabilities": edge_probabilities,
            # 批量邻接矩阵 [batch_size, num_actions, num_actions]
            "adjacency_matrices": self._create_adjacency_matrices(edge_probabilities),
            # 每个样本的节点级别嵌入列表
            "node_embeddings": node_embeddings_list,
        }

    def _predict_edge_probabilities(
        self, node_embeddings: torch.Tensor, unique_actions: torch.Tensor
    ) -> torch.Tensor:
        """预测动作间的边概率"""
        device = node_embeddings.device

        # 创建完整的动作概率矩阵
        edge_probs = torch.zeros(self.num_actions, self.num_actions, device=device)

        # 对于存在的动作，计算边概率
        num_nodes = len(unique_actions)
        if num_nodes > 1:
            node_i = node_embeddings.unsqueeze(1).expand(num_nodes, num_nodes, -1)
            node_j = node_embeddings.unsqueeze(0).expand(num_nodes, num_nodes, -1)
            pair_features = torch.cat([node_i, node_j], dim=-1)
            pair_probs = self.edge_predictor(pair_features).squeeze(-1)

            # 屏蔽对角线 (自身到自身的边)
            mask = ~torch.eye(num_nodes, dtype=torch.bool, device=device)
            pair_probs = pair_probs * mask
            # 确保在AMP下pair_probs和edge_probs dtype一致，否则索引赋值会失败
            if pair_probs.dtype != edge_probs.dtype:
                edge_probs = edge_probs.to(pair_probs.dtype)
            idx_i = unique_actions.view(-1, 1).expand(num_nodes, num_nodes)
            idx_j = unique_actions.view(1, -1).expand(num_nodes, num_nodes)
            edge_probs[idx_i, idx_j] = pair_probs

        return edge_probs

    def _create_adjacency_matrices(
        self, edge_probabilities: List[torch.Tensor]
    ) -> torch.Tensor:
        """从边概率创建邻接矩阵"""
        batch_size = len(edge_probabilities)
        device = edge_probabilities[0].device

        adj_matrices = torch.stack(
            edge_probabilities
        )  # [batch_size, num_actions, num_actions]

        return adj_matrices


class TaskGraphLoss(nn.Module):
    """任务图学习损失函数"""

    def __init__(
        self,
        structure_weight: float = 1.0,
        temporal_weight: float = 1.0,
        sparsity_weight: float = 0.1,
    ):
        super().__init__()
        self.structure_weight = structure_weight
        self.temporal_weight = temporal_weight
        self.sparsity_weight = sparsity_weight

    def forward(
        self, predictions: Dict[str, torch.Tensor], targets: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        计算任务图学习的总损失

        Args:
            predictions: 模型预测结果
            targets: 目标值

        Returns:
            包含各项损失的字典
        """
        losses = {}

        # 结构损失：鼓励学习合理的图结构
        if "edge_probabilities" in predictions:
            edge_probs_list = predictions["edge_probabilities"]
            structure_loss = self._compute_structure_loss(edge_probs_list)
            losses["structure_loss"] = structure_loss * self.structure_weight

        # 时序一致性损失
        if "adjacency_matrices" in predictions and "action_sequences" in targets:
            temporal_loss = self._compute_temporal_loss(
                predictions["adjacency_matrices"], targets["action_sequences"]
            )
            losses["temporal_loss"] = temporal_loss * self.temporal_weight

        # 稀疏性损失：避免过于密集的连接
        if "edge_probabilities" in predictions:
            sparsity_loss = self._compute_sparsity_loss(edge_probs_list)
            losses["sparsity_loss"] = sparsity_loss * self.sparsity_weight

        # 总损失
        total_loss = sum(losses.values())
        losses["total_loss"] = total_loss

        return losses

    def _compute_structure_loss(
        self, edge_probs_list: List[torch.Tensor]
    ) -> torch.Tensor:
        """计算图结构损失"""
        # 结构损失：鼓励有向无环图(DAG)结构，通过减少反向边实现
        if not edge_probs_list:
            return torch.tensor(0.0, device=torch.device('cpu'))
        device = edge_probs_list[0].device
        edge_probs = torch.stack(edge_probs_list, dim=0)
        upper_tri = torch.triu(edge_probs, diagonal=1)
        lower_tri = torch.tril(edge_probs, diagonal=-1)
        dag_losses = (lower_tri * upper_tri.transpose(-1, -2)).sum(dim=(1, 2))
        return dag_losses.mean()

    def _compute_temporal_loss(
        self, adj_matrices: torch.Tensor, action_sequences: torch.Tensor
    ) -> torch.Tensor:
        """计算时序一致性损失"""
        # 时序损失：鼓励序列转移概率较高（InfoNCE类似）
        device = adj_matrices.device
        seq_cur = action_sequences[:, :-1]
        seq_next = action_sequences[:, 1:]
        valid = (seq_cur >= 0) & (seq_next >= 0)
        if not valid.any():
            return torch.tensor(0.0, device=device)

        batch_idx = torch.arange(action_sequences.size(0), device=device).unsqueeze(1)
        probs = adj_matrices[batch_idx, seq_cur, seq_next]
        log_probs = -torch.log(probs + 1e-8)
        loss_sum = (log_probs * valid).sum(dim=1)
        counts = valid.sum(dim=1)
        valid_batch = counts > 0
        loss_per_seq = loss_sum[valid_batch] / counts[valid_batch]
        return loss_per_seq.mean()

    def _compute_sparsity_loss(
        self, edge_probs_list: List[torch.Tensor]
    ) -> torch.Tensor:
        """计算稀疏性损失"""
        # 稀疏性损失：L1 正则化鼓励稀疏连接
        if not edge_probs_list:
            return torch.tensor(0.0, device=torch.device('cpu'))
        edge_probs = torch.stack(edge_probs_list, dim=0)
        sparsity = edge_probs.sum(dim=(1, 2))
        return sparsity.mean()


def extract_task_graph(
    edge_probs: torch.Tensor, action_names: List[str], threshold: float = 0.5
) -> nx.DiGraph:
    """从边概率矩阵提取任务图"""
    num_actions = len(action_names)
    G = nx.DiGraph()

    # 添加节点
    for i, action in enumerate(action_names):
        G.add_node(i, action=action)

    # 添加边
    for i in range(num_actions):
        for j in range(num_actions):
            if i != j and edge_probs[i, j] > threshold:
                G.add_edge(i, j, weight=edge_probs[i, j].item())

    return G
