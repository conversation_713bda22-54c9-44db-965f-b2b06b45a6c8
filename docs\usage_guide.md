# 使用指南 - Linux专用版

该文档整合了Linux环境配置与基本参数说明，帮助快速运行程序性视频理解系统。

## ⚠️ 重要说明

**本系统现已完全迁移至Linux环境，仅支持Linux Ubuntu 20.04+系统。**

## 1. 环境准备

1. 确保运行在Linux Ubuntu 20.04+系统上
2. 安装Python 3.8或3.9
3. 创建虚拟环境并安装依赖：

   ```bash
   # 创建Linux服务器专用环境
   conda create -n sci_1_server python=3.8 -y
   conda activate sci_1_server
   pip install -r requirements.txt

   # 安装PyTorch (CUDA 11.8版本)
   conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
   ```

## 2. 重要配置

所有参数均在 `configs/default.yaml` 中定义，常用选项如下：

- `data.root_dir`：数据集根目录。
- `data.train_views` / `data.test_views`：训练和测试使用的视角。
- `data.num_actions`：动作类别数。
- `training.batch_size`：批次大小。
- `training.num_epochs`：训练轮数。

## 3. 运行示例

Linux服务器环境完整实验：

```bash
# 激活环境
conda activate sci_1_server

# A6000双卡配置 (Linux专用)
python scripts/train_Static.py --profile=a6000 --amp --compile

# 使用服务器配置 (推荐)
python scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile

# 多GPU分布式训练
torchrun --nproc_per_node=2 scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
```

如需进一步的技术细节，请查阅 `docs/technical_documentation.md`。
