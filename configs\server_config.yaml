# ============================================================================
# 服务器环境专用配置文件 (Server Environment Configuration)
# ============================================================================
#
# 本配置文件专门为服务器环境（双A6000 GPU）优化
# 硬件配置：2张NVIDIA A6000 48GB显卡
# 数据路径：/data2/syd_data/Breakfast_Data/
# 输出路径：/data2/syd_data/Breakfast_Data/Outputs/
#
# 使用方法：
# python scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml
# python scripts/train_Diff.py --profile=a6000 --config=configs/server_config.yaml
# ============================================================================

# ============================================================================
# 数据配置 (Data Configuration)
# ============================================================================
data:
  # 服务器数据根目录路径
  # 实际服务器路径：/data2/syd_data/Breakfast_Data/breakfast_data/
  # 包含以下结构：
  # breakfast_data/
  # ├── s1/                # 视角1的特征文件
  # ├── s2/                # 视角2的特征文件
  # ├── s3/                # 视角3的特征文件
  # └── s4/                # 视角4的特征文件
  root_dir: "/data2/syd_data/Breakfast_Data/breakfast_data"

  # 服务器标签文件根目录路径
  # 实际服务器路径：/data2/syd_data/Breakfast_Data/segmentation_coarse/
  # 包含以下结构：
  # segmentation_coarse/
  # ├── s1_label/          # 视角1的标签文件
  # ├── s2_label/          # 视角2的标签文件
  # ├── s3_label/          # 视角3的标签文件
  # └── s4_label/          # 视角4的标签文件
  label_root_dir: "/data2/syd_data/Breakfast_Data/segmentation_coarse"

  # 训练使用的视角列表
  # breakfast数据集包含4个视角：s1(stereo_01), s2(stereo_02), s3(webcam_01), s4(webcam_02)
  # 建议使用多个视角进行训练以提高模型的泛化能力
  train_views: ["s1", "s2", "s3"]

  # 测试使用的视角列表
  # 通常选择与训练视角不同的视角来评估跨视角泛化能力
  test_views: ["s4"]

  # 预提取特征的维度
  # 根据实际数据文件分析，当前数据集特征维度为65维
  # 确保与实际特征文件的维度一致
  feature_dim: 65

  # 最大序列长度（帧数）
  # 用于padding和截断序列，根据数据集中最长视频的长度设置
  # 当前cereals数据集视频长度约为800-900帧
  max_sequence_length: 1200

  # 动作类别总数
# 数量将从实际数据中动态确定，无需在配置文件中指定

# ============================================================================
# 模型配置 (Model Configuration) - A6000优化
# ============================================================================
model:
  # 静态任务图配置 - A6000优化
  task_graph:
    hidden_dim: 768          # 增大隐藏层维度，充分利用A6000显存
    num_gnn_layers: 4        # 增加GNN层数，提高模型表达能力
    dropout: 0.1
    graph_pooling: "mean"

  # 动作原型特征配置 - A6000优化
  prototype:
    prototype_dim: 512       # 增大原型维度
    num_prototypes_per_action: 5  # 增加原型数量
    temperature: 0.1
    momentum: 0.999

  # 多视角融合配置 - A6000优化
  fusion:
    view_encoder_dim: 768    # 增大编码器维度
    fusion_method: "concat"
    domain_adaptation: true
    uncertainty_weighting: true  # 启用不确定性加权

# ============================================================================
# 训练配置 (Training Configuration) - A6000优化
# ============================================================================
training:
  # 批次大小 - A6000专用优化
  # 双A6000 (48GB×2): 128-160 (自动调整)
  # 单A6000 (48GB): 96-128
  batch_size: 128

  # 学习率 - 大批次训练优化
  learning_rate: 0.0005    # 适当增加学习率以适应大批次

  # 权重衰减
  weight_decay: 0.01

  # 训练轮数 - 服务器环境可以训练更多轮次
  num_epochs: 10

  # 预热轮数
  warmup_epochs: 5

  # 学习率调度器
  scheduler: "cosine"

  # A6000硬件优化配置
  hardware:
    tf32: true              # 启用TF32加速
    enable_compile: true    # 启用模型编译 (torch.compile 优化)
    flash_attention: false  # 禁用Flash Attention (当前LSTM架构不支持)

  # 损失函数权重
  loss_weights:
    action_classification: 1.0
    prototype_contrastive: 0.5
    task_graph_structure: 0.3
    temporal_consistency: 0.2
    view_alignment: 0.1

  # GPU优化配置 - A6000专用
  gpu_optimization:
    enable_amp: true        # 混合精度训练
    num_workers: 16         # A6000专用：16个worker
    pin_memory: true
    gradient_accumulation_steps: 1
    max_memory_fraction: 0.98
    enable_memory_cleanup: true
    memory_cleanup_freq: 150
    enable_non_blocking: true
    enable_compile: true    # Linux环境启用torch.compile

    # A6000专用优化
    a6000_optimizations:
      enable_tf32: true
      enable_jit_compile: true
      enable_channels_last: true
      
      # 多GPU并行配置
      multi_gpu:
        backend: "nccl"
        find_unused_parameters: false
        bucket_cap_mb: 25

      # 数据加载器优化
      dataloader:
        persistent_workers: true
        prefetch_factor: 6

# ============================================================================
# 评估配置 (Evaluation Configuration)
# ============================================================================
evaluation:
  metrics:
    - "frame_accuracy"
    - "segment_accuracy"
    - "edit_distance"
    - "f1_score"
    - "graph_similarity"
    - "prototype_quality"

  visualization:
    save_prototype_clusters: true
    save_task_graphs: true

# ============================================================================
# 实验配置 (Experiment Configuration) - 服务器环境
# ============================================================================
experiment:
  name: "breakfast_task_graph_server_a6000"

  # 服务器输出目录：/data2/syd_data/Breakfast_Data/Outputs/
  # 包含以下子目录：
  # - checkpoints/: 模型检查点
  # - visualizations/: 图片和可视化
  # - reports/: 实验报告

  # 模型检查点保存频率（轮数）
  checkpoint_freq: 5       # 服务器环境更频繁保存

  # 日志记录频率（批次）
  log_freq: 50            # 更频繁的日志记录

  # 随机种子
  seed: 42

# ============================================================================
# 服务器环境说明
# ============================================================================
#
# 1. 硬件配置：
#    - GPU: 2张NVIDIA A6000 48GB
#    - 内存: 充足的系统内存
#    - 存储: 高速SSD存储
#
# 2. 数据路径：
#    - 原始数据：/data2/syd_data/Breakfast_Data/breakfast_data/
#    - 标签数据：/data2/syd_data/Breakfast_Data/segmentation_coarse/
#    - 输出路径：/data2/syd_data/Breakfast_Data/Outputs/
#
# 3. 性能优化：
#    - 启用所有A6000专用优化
#    - 使用NCCL后端进行多GPU通信
#    - 启用TF32、JIT编译、Flash Attention
#    - 优化的批次大小和数据加载配置
#
# 4. 训练命令：
#    # 静态模型训练
#    python scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
#    
#    # 差分模型训练
#    python scripts/train_Diff.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
#
# 5. 多GPU训练：
#    # 使用torchrun启动多GPU训练
#    torchrun --nproc_per_node=2 scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
#
# ============================================================================
