# API 文档

## 概述

本文档描述了程序性视频理解系统的主要API接口和模块。

## 核心模块

### 1. 训练工具模块 (`src/training_utils.py`)

#### `parse_training_cli(description: str) -> argparse.Namespace`

解析训练脚本的命令行参数。

**参数:**

- `description`: 脚本描述字符串

**返回:**

- `argparse.Namespace`: 解析后的命令行参数

**示例:**

```python
from training_utils import parse_training_cli
args = parse_training_cli("静态模型训练")
```

#### `validate_platform_compatibility(profile: str) -> None`

验证硬件配置与Linux系统的兼容性。

**参数:**

- `profile`: 硬件配置文件 ("a6000")

**说明:**

- 专为Linux环境设计，无需平台检查

#### `init_device_and_distributed(profile: str) -> torch.device`

初始化计算设备和分布式训练环境。

**参数:**

- `profile`: 硬件配置文件

**返回:**

- `torch.device`: 计算设备对象

#### `setup_hardware_optimization(profile: str, device: torch.device) -> Dict[str, Any]`

设置硬件专用优化配置。

**参数:**

- `profile`: 硬件配置文件
- `device`: 计算设备

**返回:**

- `Dict[str, Any]`: 优化配置字典

#### `create_optimizer(model: nn.Module, config: Dict, optimization_config: Dict) -> torch.optim.Optimizer`

创建适合当前平台的优化器。

**参数:**

- `model`: PyTorch模型
- `config`: 训练配置
- `optimization_config`: 硬件优化配置

**返回:**

- `torch.optim.Optimizer`: 优化器对象

### 2. 报告生成模块 (`src/report_generator.py`)

#### `TrainingReportGenerator`

训练报告生成器类。

##### `__init__(model_type: str = "static")`

初始化报告生成器。

**参数:**

- `model_type`: 模型类型 ("static" 或 "differential")

##### `generate_training_curves(train_history: Dict, profile: str) -> Path`

生成训练曲线图。

**参数:**

- `train_history`: 训练历史数据
- `profile`: 硬件配置文件

**返回:**

- `Path`: 图表保存路径

##### `generate_markdown_report(...) -> Path`

生成详细的Markdown格式训练报告。

**返回:**

- `Path`: 报告保存路径

#### `InferenceReportGenerator`

推理报告生成器类。

##### `generate_comparison_report(...) -> Path`

生成模型对比报告。

**返回:**

- `Path`: 报告保存路径

### 3. 评估模块 (`src/evaluation_simple.py`)

#### `Evaluator`

模型评估器类。

##### `__init__(config: Dict)`

初始化评估器。

**参数:**

- `config`: 配置字典

##### `evaluate_predictions(predictions: np.ndarray, labels: np.ndarray) -> Dict[str, float]`

评估预测结果。

**参数:**

- `predictions`: 预测结果数组
- `labels`: 真实标签数组

**返回:**

- `Dict[str, float]`: 评估指标字典

**指标包括:**

- `frame_accuracy`: 帧级别准确率
- `segment_accuracy`: 段级别准确率
- `edit_distance`: 编辑距离
- `f1_score`: F1分数

##### `evaluate(model, test_loader, device) -> Dict[str, float]`

在测试集上评估模型性能。

**参数:**

- `model`: 训练好的模型
- `test_loader`: 测试数据加载器
- `device`: 计算设备

**返回:**

- `Dict[str, float]`: 评估指标字典

### 4. 主模型 (`src/model.py`)

#### `ProceduralVideoUnderstanding`

程序性视频理解主模型类。

##### `__init__(config: Dict)`

初始化模型。

**参数:**

- `config`: 模型配置字典

##### `forward(batch: Dict[str, torch.Tensor], mode: str = "train") -> Dict[str, torch.Tensor]`

模型前向传播。

**参数:**

- `batch`: 输入批次数据
- `mode`: 运行模式 ("train" 或 "eval")

**返回:**

- `Dict[str, torch.Tensor]`: 模型输出字典

**输出包括:**

- `action_logits`: 动作分类logits
- `action_predictions`: 动作预测
- `prototype_outputs`: 原型学习输出
- `task_graph_outputs`: 任务图输出

##### `compute_loss(outputs: Dict[str, torch.Tensor], batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]`

计算模型损失。

**参数:**

- `outputs`: 模型输出
- `batch`: 输入批次数据

**返回:**

- `Dict[str, torch.Tensor]`: 损失字典

### 5. 数据加载器 (`src/data_loader_fixed.py`)

#### `create_data_loaders(config: Dict) -> Tuple[DataLoader, DataLoader]`

创建训练和测试数据加载器。

**参数:**

- `config`: 数据配置字典

**返回:**

- `Tuple[DataLoader, DataLoader]`: 训练和测试数据加载器

## 配置文件格式

### 主配置文件 (`configs/default.yaml`)

```yaml
data:
  root_dir: "数据根目录路径"
  label_root_dir: "标签根目录路径"
  train_views: ["s1", "s2", "s3"]
  test_views: ["s4"]
  feature_dim: 65
  max_sequence_length: 1200
  num_actions: 5

model:
  task_graph:
    hidden_dim: 512
    num_gnn_layers: 3
    dropout: 0.1
  prototype:
    prototype_dim: 256
    num_prototypes_per_action: 3
    temperature: 0.1

training:
  batch_size: 32
  learning_rate: 0.0003
  weight_decay: 0.01
  num_epochs: 10
```

## 使用示例

### 训练静态模型

```python
# 使用命令行
python scripts/train_Static.py --profile=3060ti --amp

# 或使用代码
from training_utils import *
from report_generator import TrainingReportGenerator

# 解析参数
args = parse_training_cli("静态模型训练")

# 初始化设备
device = init_device_and_distributed(args.profile)

# 设置优化
optimization_config = setup_hardware_optimization(args.profile, device)

# 生成报告
report_gen = TrainingReportGenerator("static")
report_path = report_gen.generate_markdown_report(...)
```

### 训练差分模型

```python
# 使用命令行
python scripts/train_Diff.py --profile=3060ti --static-model=path/to/model.pth --amp

# 差分模型需要预训练的静态模型
```

### 模型评估

```python
from evaluation_simple import Evaluator

# 创建评估器
evaluator = Evaluator(config)

# 评估模型
results = evaluator.evaluate(model, test_loader, device)
print(f"帧级准确率: {results['frame_accuracy']:.4f}")
print(f"段级准确率: {results['segment_accuracy']:.4f}")
```

### 性能对比测试

```python
# 使用命令行
python Test/test_static_vs_differential_comparison.py

# 生成详细的对比报告
```

## 错误处理

### 常见错误

1. **CUDA内存不足**

   ```
   RuntimeError: CUDA out of memory
   ```

   解决方案: 减小批处理大小或启用梯度累积

2. **配置错误**

   ```
   RuntimeError: 配置文件格式错误
   ```

   解决方案: 检查YAML配置文件语法

3. **模型文件不存在**

   ```
   FileNotFoundError: 静态模型文件不存在
   ```

   解决方案: 检查模型路径或先训练静态模型

## 性能优化建议

1. **硬件配置**
   - A6000: 使用batch_size=96-128, num_workers=8-16
   - 双A6000: 使用batch_size=160, num_workers=16-20

2. **内存管理**
   - 启用混合精度训练 (`--amp`)
   - 定期清理GPU缓存
   - 使用梯度累积处理大批次

3. **训练策略**
   - 差分模型使用较小的学习率缩放因子 (0.1-0.3)
   - 监控训练曲线，及时调整超参数
   - 使用学习率调度器优化收敛

## 扩展开发

### 添加新的评估指标

```python
# 在 evaluation_simple.py 中添加新方法
def evaluate_custom_metric(self, predictions, labels):
    # 实现自定义评估逻辑
    return metric_value
```

### 添加新的模型组件

```python
# 在 model.py 中添加新组件
class CustomComponent(nn.Module):
    def __init__(self, config):
        super().__init__()
        # 初始化组件
    
    def forward(self, x):
        # 前向传播逻辑
        return output
```

### 自定义报告格式

```python
# 继承 TrainingReportGenerator
class CustomReportGenerator(TrainingReportGenerator):
    def generate_custom_report(self, data):
        # 实现自定义报告逻辑
        return report_path
```
