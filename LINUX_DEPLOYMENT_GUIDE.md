# Linux部署指南

## 🎯 概述

本系统已完全迁移至Linux环境，专为Linux Ubuntu 20.04+和NVIDIA A6000 GPU优化。

## ⚠️ 系统要求

- **操作系统**: Linux Ubuntu 20.04+ (仅支持Linux)
- **硬件**: NVIDIA A6000 GPU (推荐双卡配置)
- **Python**: 3.8-3.9
- **CUDA**: 11.8+
- **内存**: 建议64GB+系统内存

## 🚀 快速部署

### 1. 环境准备

```bash
# 检查系统
uname -a  # 确认Linux系统

# 检查GPU
nvidia-smi  # 确认A6000 GPU

# 使用一键部署脚本
chmod +x deploy_server.sh
./deploy_server.sh
```

### 2. 手动部署（可选）

```bash
# 创建环境
conda create -n sci_1_server python=3.8 -y
conda activate sci_1_server

# 安装依赖
pip install -r requirements.txt
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
```

### 3. 数据配置

确保数据路径结构如下：
```
/data2/syd_data/Breakfast_Data/
├── breakfast_data/
│   ├── s1/
│   ├── s2/
│   ├── s3/
│   └── s4/
├── segmentation_coarse/
│   ├── s1_label/
│   ├── s2_label/
│   ├── s3_label/
│   └── s4_label/
└── Outputs/  # 自动创建
```

## 🏃‍♂️ 运行训练

### 静态模型训练

```bash
# 激活环境
conda activate sci_1_server

# 单GPU训练
python scripts/train_Static.py --profile=a6000 --amp --compile

# 双GPU分布式训练（推荐）
torchrun --nproc_per_node=2 scripts/train_Static.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
```

### 差分模型训练

```bash
# 需要先完成静态模型训练
python scripts/train_Diff.py --profile=a6000 --config=configs/server_config.yaml --amp --compile
```

## 📊 输出结果

训练完成后，结果保存在：
```
/data2/syd_data/Breakfast_Data/Outputs/
├── checkpoints/     # 模型权重
├── pictures/        # 可视化图片
└── reports/         # 训练报告
```

## 🔧 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 减小批处理大小
   # 编辑 configs/server_config.yaml 中的 batch_size
   ```

2. **数据路径错误**
   ```bash
   # 检查数据目录结构
   ls -la /data2/syd_data/Breakfast_Data/
   ```

3. **权限问题**
   ```bash
   # 确保有写入权限
   chmod -R 755 /data2/syd_data/Breakfast_Data/
   ```

## 📝 重要说明

- 本系统不再支持Windows平台
- 所有平台检查代码已移除
- 专为Linux环境优化
- 测试代码已清理，专注核心功能

## 🎉 迁移完成

系统已成功迁移至Linux专用环境，可以安全部署到生产服务器。
