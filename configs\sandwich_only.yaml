# Configuration for testing only the sandwich task
# Based on default.yaml but modified to only load sandwich task

# Data configuration
data:
  root_dir: "/data2/syd_data/Breakfast_Data/breakfast_data"
  label_root_dir: "/data2/syd_data/Breakfast_Data/segmentation_coarse"

  # Only test sandwich task in s1, s2, s3, s4 folders
  train_views: ["s1", "s2", "s3"]
  test_views: ["s4"]
  target_tasks: ["sandwich"]  # NEW: Only load sandwich task

  feature_dim: 65
  max_sequence_length: 1200

# Model configuration (simplified for testing)
model:
  task_graph:
    hidden_dim: 256
    num_gnn_layers: 2
    dropout: 0.1
    graph_pooling: "mean"

  prototype:
    prototype_dim: 128
    num_prototypes_per_action: 2
    temperature: 0.1
    momentum: 0.999

  fusion:
    view_encoder_dim: 256
    fusion_method: "concat"
    domain_adaptation: true
    uncertainty_weighting: false

# Training configuration (small for testing)
training:
  batch_size: 8
  learning_rate: 0.001
  weight_decay: 0.01
  num_epochs: 10
  warmup_epochs: 1
  scheduler: "cosine"

  hardware:
    tf32: true
    enable_compile: false
    # Flash Attention不适用于当前LSTM+简化融合架构
    flash_attention: false

  loss_weights:
    action_classification: 1.0
    prototype_contrastive: 0.5
    task_graph_structure: 0.3
    temporal_consistency: 0.2
    view_alignment: 0.1

  gpu_optimization:
    enable_amp: false
    num_workers: 2
    pin_memory: true
    gradient_accumulation_steps: 1
    max_memory_fraction: 0.8
    enable_memory_cleanup: true
    memory_cleanup_freq: 50
    enable_non_blocking: true
    enable_compile: false

# Evaluation configuration
evaluation:
  metrics:
    - "frame_accuracy"
    - "segment_accuracy"
    - "f1_score"

  visualization:
    save_prototype_clusters: false
    save_task_graphs: false

# Experiment configuration
experiment:
  name: "sandwich_only_test"
  checkpoint_freq: 5
  log_freq: 50
  seed: 42
